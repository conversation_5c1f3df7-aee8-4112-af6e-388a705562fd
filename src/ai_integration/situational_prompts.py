#!/usr/bin/env python3
"""
🎯 SITUATIONAL ANALYSIS PROMPTS - TWO-STAGE DISCOVERY SYSTEM

Revolutionary two-stage pattern discovery system:
Stage 1: <PERSON> methodology for sophisticated pattern discovery
Stage 2: Translation to backtesting-compatible format

This module implements Stage 1: Enhanced Discovery using <PERSON>'s proven methodology.
"""

class TomHougaardDiscoveryPrompts:
    """Generate Tom Ho<PERSON> methodology prompts for sophisticated pattern discovery"""
    
    @staticmethod
    def get_tom_hougaard_core_principles():
        """Core Tom <PERSON> principles for CFD PROFIT pattern discovery"""
        return [
            "Focus on identifying recurring PROFITABLE market situations in CFD trading with 1:100 leverage",
            "Develop statistical frameworks based on what actually GENERATES PROFITS in leveraged markets",
            "Think in terms of 'When X situation occurs, Y PROFITABLE outcome follows with Z frequency'",
            "Remember: 'Anything can happen' on any individual trade, but PROFITABLE patterns provide statistical edges over time",
            "Use ONLY Open, High, Low, Close data for MAXIMUM PROFIT clarity - no indicators whatsoever",
            "Rely on human pattern recognition focused on PROFIT-GENERATING market context understanding",
            "Focus on geometric relationships and price behavior patterns that CREATE PROFITS in CFD trading",
            "Analyze 'naked charts' to see what price is actually doing to GENERATE MAXIMUM PROFITS",
            "Every pattern must be designed to exploit 1:100 leverage for AMPLIFIED PROFIT GENERATION"
        ]
    
    @staticmethod
    def get_discovery_methodology_steps():
        """Tom Hougaard's discovery methodology steps"""
        return [
            "Conduct extensive historical chart analysis spanning multiple years",
            "Examine thousands of trading sessions manually (not algorithmic backtesting)",
            "Look for recurring price behaviors that happen with measurable frequency",
            "Cross-correlate patterns across different market conditions and timeframes",
            "Document pattern frequency and success rates through manual tracking",
            "Identify patterns that occur with consistent frequency (minimum hundreds of occurrences)",
            "Calculate actual win rates and risk-reward ratios",
            "Validate patterns across different market regimes (trending, ranging, volatile, quiet)",
            "Ensure patterns maintain edge across extended time periods",
            "Aim for statistical significance (60%+ win rates for viable patterns)"
        ]
    
    @staticmethod
    def get_situational_context_requirements():
        """Situational context integration requirements"""
        return [
            "Understand WHEN patterns work best (specific market hours, days, conditions)",
            "Recognize that patterns are situational, not mechanical",
            "Identify market memory effects (how markets react to previous important levels)",
            "Consider multi-timeframe context and trend alignment",
            "Analyze market regime sensitivity (bull/bear markets, high/low volatility periods)",
            "Investigate opening behaviors, closing behaviors, specific hour tendencies",
            "Analyze multi-day relationships (Monday-Friday correlations, week-to-week patterns)",
            "Explore intraday volatility cycles and their exploitable characteristics",
            "Look for seasonal or cyclical behavioral patterns"
        ]
    
    @staticmethod
    def get_pattern_categories_to_explore():
        """ORB-focused pattern categories for exploration using Tom Hougaard methodology"""
        return {
            "Opening Range Breakout Patterns": [
                "1-candle ORB breakouts (first candle opening range)",
                "2-candle ORB breakouts (first two candles opening range)",
                "3-candle ORB breakouts (first three candles opening range)",
                "4-6 candle ORB breakouts (extended opening range periods)"
            ],
            "Session-Specific ORB Behaviors": [
                "London session ORB patterns (9:00-17:30 UTC+1)",
                "New York session ORB patterns (15:30-22:00 UTC+1)",
                "Asian session ORB patterns (1:00-7:00 UTC+1)",
                "London-NY overlap ORB patterns (15:30-17:30 UTC+1)",
                "All-session ORB patterns (24-hour trading)"
            ],
            "ORB Timeframe Correlations": [
                "1-minute ORB breakout confirmations",
                "5-minute ORB momentum patterns",
                "15-minute ORB institutional flows",
                "30-60 minute ORB major moves"
            ],
            "ORB Range Size and Volatility": [
                "Small range ORB breakouts (tight opening ranges)",
                "Large range ORB breakouts (wide opening ranges)",
                "ORB range expansion after breakout",
                "ORB failure patterns (false breakouts)"
            ],
            "Sophisticated ORB Exit Strategies": [
                "Trail stops below previous 2 candle lows until pattern invalidates",
                "Exit when price closes back inside opening range (pattern failure)",
                "Session transition exits 30 minutes before new session opens",
                "Break of structure exits when key support/resistance fails",
                "Adaptive trailing based on volatility and time of day",
                "Profit targets at session high/low test levels",
                "Time-based exits during low-liquidity periods",
                "Volume-based exits when institutional flow reverses"
            ]
        }
    
    @staticmethod
    def get_discovery_examples():
        """ORB-focused Tom Hougaard-style discovery examples to inspire thinking"""
        return [
            "When London session opens and price breaks above the first 2-candle opening range high with strong momentum, institutional flows typically drive price higher as retail participants chase the ORB breakout, creating a 3:1 risk-reward opportunity",
            "During New York session, when price breaks below the first 3-candle opening range low, institutional selling pressure typically accelerates as stop losses are triggered, creating predictable downward momentum for 2-4 hours",
            "When the opening range (first 30 minutes) is unusually tight (less than 20 pips), and price breaks either direction, the breakout typically extends at least 2x the opening range size due to compressed volatility release and institutional FOMO",
            "After London session establishes a 4-candle opening range, when price breaks above the range high during London-NY overlap (15:30-17:30 UTC+1), institutional flows typically push price to test the daily high within 2 hours due to combined session momentum",
            "When Asian session creates a narrow 6-candle opening range, and London session opens with a gap above the range high, retail participants typically chase the gap creating ORB momentum that persists for the entire London session",
            "During high volatility periods, when the first 1-candle opening range is unusually wide (50+ pips), and price breaks below the range low, institutional selling typically accelerates creating extended downward moves of 2-3x the opening range size"
        ]
    
    @staticmethod
    def generate_stage1_discovery_prompt(ohlc_data, market_summaries="", performance_feedback=""):
        """
        Generate Stage 1: Tom Hougaard Discovery Prompt
        
        This prompt focuses purely on sophisticated pattern discovery using Tom Hougaard's methodology.
        No constraints on format - pure creative discovery.
        
        Args:
            ohlc_data: Market data for context
            market_summaries: Behavioral intelligence summaries
            performance_feedback: Previous session learning data
            
        Returns:
            Stage 1 discovery prompt string
        """
        
        # Enforce strict OHLCV capitalization and fail-fast if missing
        required_cols = ['Open', 'High', 'Low', 'Close']
        missing = [col for col in required_cols if col not in ohlc_data.columns]
        if missing:
            raise RuntimeError(f'UNBREAKABLE RULE VIOLATION: Missing OHLC columns: {missing}')
        
        core_principles = TomHougaardDiscoveryPrompts.get_tom_hougaard_core_principles()
        methodology_steps = TomHougaardDiscoveryPrompts.get_discovery_methodology_steps()
        context_requirements = TomHougaardDiscoveryPrompts.get_situational_context_requirements()
        pattern_categories = TomHougaardDiscoveryPrompts.get_pattern_categories_to_explore()
        discovery_examples = TomHougaardDiscoveryPrompts.get_discovery_examples()
        
        # Add market regime analysis
        regime_analysis = TomHougaardDiscoveryPrompts._analyze_market_regime(ohlc_data)
        
        prompt = f"""🎯 STAGE 1: ORB-FOCUSED CFD PATTERN DISCOVERY USING TOM HOUGAARD METHODOLOGY

You are a master Opening Range Breakout (ORB) pattern detective using Tom Hougaard's proven methodology to discover completely novel, HIGHLY PROFITABLE ORB trading patterns. Your mission is to uncover hidden statistical edges in Opening Range Breakout patterns that generate MAXIMUM PROFITABILITY in CFD trading with 1:100 leverage.

🔥 CRITICAL CONSTRAINT: ORB PATTERNS ONLY
You MUST discover ONLY Opening Range Breakout patterns. NO other pattern types allowed. Focus exclusively on patterns where price breaks above or below the opening range (first 1-6 candles of a session) to generate consistent, high-probability profits in leveraged CFD trading.

💰 CRITICAL FOCUS: ORB PROFITABILITY IS EVERYTHING
This is CFD trading with 1:100 leverage. Every ORB pattern you discover MUST be designed for MAXIMUM PROFIT GENERATION. ORB patterns that don't generate substantial profits are worthless. Focus exclusively on Opening Range Breakout patterns that can deliver consistent, high-probability profits.

🧠 CORE ORB-FOCUSED CFD TRADING PHILOSOPHY TO ADOPT:
{chr(10).join([f'• {principle}' for principle in core_principles])}

💰 CFD TRADING SPECIFICS - MANDATORY CONSIDERATIONS:
• This is CFD trading with 1:100 leverage - small price movements create large profits/losses
• Focus on patterns that exploit leverage effectively for maximum profit generation
• Consider the amplified risk/reward dynamics of leveraged trading
• Patterns must be designed for quick, profitable entries and exits
• Every pattern MUST have clear profit targets that justify the leveraged risk

📊 DISCOVERY METHODOLOGY TO FOLLOW:
{chr(10).join([f'• {step}' for step in methodology_steps])}

🎯 SITUATIONAL CONTEXT REQUIREMENTS:
{chr(10).join([f'• {req}' for req in context_requirements])}

📈 MARKET REGIME ANALYSIS:
{regime_analysis}

🔍 ENHANCED BEHAVIORAL ANALYSIS:
{market_summaries}

📚 LEARNING FROM PREVIOUS SESSIONS:
{performance_feedback}

🎨 PATTERN CATEGORIES TO EXPLORE:
"""
        
        for category, patterns in pattern_categories.items():
            prompt += f"\n**{category}:**\n"
            for pattern in patterns:
                prompt += f"  • {pattern}\n"
        
        prompt += f"""
🌟 DISCOVERY EXAMPLES TO INSPIRE YOUR THINKING:
(These are examples of the TYPE of sophisticated thinking to use - create your own original patterns)

{chr(10).join([f'• {example}' for example in discovery_examples])}

🎯 YOUR ORB-FOCUSED CFD PROFITABILITY DISCOVERY MISSION:

Discover 3-5 completely NOVEL, HIGHLY PROFITABLE Opening Range Breakout (ORB) patterns that:

1. **MAXIMIZE ORB PROFITABILITY** - Every pattern must be ORB-focused for maximum profit generation in CFD trading
2. **Exploit flexible opening ranges** - Use 1-6 candle opening ranges (whatever works best for profits)
3. **Focus on session-specific ORB behaviors** - London, NY, Asian session ORB patterns
4. **Follow Tom Hougaard's ORB methodology** - Pure ORB price action, no indicators
5. **Are statistically validated ORB patterns** - Show clear ORB frequency, success rate, and PROFIT POTENTIAL
6. **Are ORB situationally contextualized** - Explain when and why ORB patterns generate maximum profits
7. **Demonstrate clear ORB profit edge** - Statistical basis for ORB profit generation
8. **Exploit ORB behavioral inefficiencies** - Focus on participant psychology during opening range breakouts

🔬 ORB-FOCUSED CFD PROFITABILITY DISCOVERY OUTPUT FORMAT:

For each HIGHLY PROFITABLE ORB pattern you discover, provide:

**ORB PATTERN [X]: [Descriptive Name] - ORB CFD PROFIT MAXIMIZER**
ORB Market Situation: [Detailed description of the specific ORB situation this pattern exploits for MAXIMUM PROFIT]
ORB Opening Range: [Specify the opening range period - 1, 2, 3, 4, 5, or 6 candles that works best]
ORB Session Context: [London, NY, Asian, or overlap session where this ORB pattern works best]
ORB Participant Behavior: [Explain WHY participants behave predictably during ORB breakouts creating PROFIT OPPORTUNITIES]
ORB Statistical Edge: [Statistical basis for ORB PROFIT GENERATION - frequency, win rate, risk-reward for ORB patterns]
ORB Multi-Timeframe Context: [How different timeframes (1min, 5min, 15min, 30min, 60min) enhance ORB profitability]
ORB Breakout Direction: [Above range high, below range low, or both - specify optimal ORB direction]
ORB Optimal Conditions: [Specific ORB conditions, session times, or range sizes that generate MAXIMUM PROFITS]
ORB CFD Leverage Advantage: [How this ORB pattern specifically exploits 1:100 leverage for amplified profits]
ORB Profit Expectations: [Expected ORB profit targets, win rates, and frequency for this ORB pattern]
ORB Behavioral Logic: [The deeper behavioral/institutional logic that creates predictable ORB responses]

🚨 CRITICAL ORB REQUIREMENTS:

• Focus on ORB DISCOVERY ONLY - no other pattern types allowed
• Each ORB pattern must exploit specific opening range breakout behavioral inefficiencies
• ORB patterns must be based on recurring opening range situations, not one-off events
• Include ORB multi-timeframe context and session-specific awareness
• Explain the participant psychology that creates ORB edges
• Use flexible opening range periods (1-6 candles) - whatever generates maximum profits
• Think like Tom Hougaard - ORB situational, behavioral, statistical
• MANDATORY: All patterns must involve opening range breakouts (above or below)

Remember: You are discovering NEW ORB patterns using Tom Hougaard's methodology. Think like an ORB detective, using statistical analysis and situational awareness to uncover hidden edges in opening range breakout behaviors.

Begin your sophisticated pattern discovery now:"""

        return prompt
    
    @staticmethod
    def _analyze_market_regime(ohlc_data):
        """Analyze market regime to guide pattern discovery"""
        try:
            # Calculate basic regime metrics
            total_return = ((ohlc_data['Close'].iloc[-1] / ohlc_data['Close'].iloc[0]) - 1) * 100
            volatility = ohlc_data['Close'].pct_change().std() * 100
            
            # Determine regime and bias
            if total_return > 5 and volatility > 2:
                regime = "STRONG UPTREND"
                bias = "🚀 BULLISH MOMENTUM - Focus on continuation patterns and breakout strategies"
                pattern_guidance = "Look for momentum continuation patterns, institutional accumulation signals"
            elif total_return < -5 and volatility > 2:
                regime = "STRONG DOWNTREND"
                bias = "🐻 BEARISH MOMENTUM - Focus on breakdown patterns and short-side opportunities"
                pattern_guidance = "Look for momentum breakdown patterns, institutional distribution signals"
            elif volatility < 1:
                regime = "LOW VOLATILITY"
                bias = "😴 CONSOLIDATION - Focus on range-bound patterns and volatility expansion signals"
                pattern_guidance = "Look for compression patterns, volatility breakout setups"
            elif volatility > 3:
                regime = "HIGH VOLATILITY"
                bias = "⚡ VOLATILE - Focus on mean reversion and volatility contraction patterns"
                pattern_guidance = "Look for overextension patterns, volatility normalization signals"
            else:
                regime = "BALANCED"
                bias = "⚖️ NEUTRAL - Use both directional and mean reversion approaches"
                pattern_guidance = "Look for balanced patterns that work in multiple market conditions"
            
            return f"""
📊 Market Regime: {regime} ({total_return:+.2f}% total return)
📊 Volatility Level: {volatility:.2f}% (daily price movement)
{bias}
🧠 Discovery Focus: {pattern_guidance}

⚠️ CRITICAL: Align your pattern discovery with this regime analysis.
   Focus on patterns that exploit the current market behavioral dynamics."""
        
        except Exception as e:
            return f"Market regime analysis unavailable: {e}"
